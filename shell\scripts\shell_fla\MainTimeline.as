package shell_fla
{
   import com.junkbyte.console.Cc;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.net.LocalConnection;
   import flash.net.URLRequest;
   import flash.system.Security;
   import flash.utils.getDefinitionByName;
   import flash.utils.Timer;
   import flash.events.TimerEvent;

   public dynamic class MainTimeline extends MovieClip
   {
      public var conn:LocalConnection;

      public var m_l:Loader;

      public var XingLingFactory:Class;

      // 新增：缓存常用的游戏类引用
      private var m_gamingUI:Class;
      private var m_xmlSingle:Class;
      private var m_myFunction:Class;
      private var m_myFunction2:Class;

      // 调试标志
      private var debugMode:Boolean = true;
      private var gameLoaded:Boolean = false;

      public function MainTimeline()
      {
         super();
         addFrameScript(0,this.frame1);
         debugLog("MainTimeline 构造函数执行完成");


      }


      
      public function __(param1:Event) : void
      {
         debugLog("游戏SWF加载完成，开始初始化");
         this.addChild(this.m_l);
         gameLoaded = true;

         // 延迟初始化游戏类，确保游戏完全加载
         setTimeout(function():void {
            initGameClasses();
            debugLog("游戏类初始化完成，准备连接LocalConnection");
         }, 1000);

         stage.addEventListener(MouseEvent.CLICK,this.___);
      }

      public function ___(param1:Event) : void
      {
         debugLog("尝试连接LocalConnection");
         try {
            this.conn.connect("my_cons");
            debugLog("LocalConnection连接成功");
         } catch (e:Error) {
            debugLog("LocalConnection连接失败: " + e.message);
         }
         stage.removeEventListener(MouseEvent.CLICK,this.___);
      }

      // 调试日志函数
      private function debugLog(message:String):void
      {
         if (debugMode) {
            trace("[DEBUG] " + new Date().toTimeString() + " - " + message);
            Cc.log("[DEBUG] " + message);
         }
      }

      // 延迟执行函数
      private function setTimeout(func:Function, delay:int):void
      {
         var timer:Timer = new Timer(delay, 1);
         timer.addEventListener(TimerEvent.TIMER, function(e:TimerEvent):void {
            func();
            timer.stop();
         });
         timer.start();
      }
      
      public function save(param1:Object) : void
      {
         debugLog("=== 开始执行外挂功能 ===");
         debugLog("接收到的参数: " + JSON.stringify(param1));



         // 检查游戏是否已加载
         if (!gameLoaded) {
            debugLog("错误：游戏尚未加载完成");
            return;
         }

         // 获取玩家编号
         var playerIndex:int = param1["P"];
         var functionType:int = param1["type"];
         var itemId:String = param1["id"];
         var itemNum:String = param1["num"];

         debugLog("玩家编号: " + playerIndex);
         debugLog("功能类型: " + functionType);
         debugLog("物品ID: " + itemId);
         debugLog("数量: " + itemNum);

         // 初始化游戏类引用
         if (!initGameClasses()) {
            debugLog("错误：游戏类初始化失败");
            return;
         }

         // 获取玩家对象
         var player:Object = getPlayerObject(playerIndex);
         if (!player) {
            debugLog("错误：无法获取玩家对象");
            return;
         }

         debugLog("成功获取玩家对象，开始分析玩家数据结构");
         analyzePlayerStructure(player);

         // 执行对应的修改功能
         debugLog("开始执行功能类型: " + functionType);

         try {
            switch(functionType)
            {
               case 1: // 添加装备
                  addEquipmentFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 2: // 清理装备背包
                  clearPackageFunction(player, "equipment");
                  break;
               case 3: // 添加宝石
                  addGemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 4: // 清理宝石背包
                  clearPackageFunction(player, "gem");
                  break;
               case 5: // 添加消耗品
                  addConsumableFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 6: // 清理消耗品背包
                  clearPackageFunction(player, "consumable");
                  break;
               case 7: // 添加其他道具
                  addOtherItemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 8: // 清理其他道具背包
                  clearPackageFunction(player, "other");
                  break;
               case 9: // 添加任务道具
                  addQuestItemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 10: // 清理任务道具背包
                  clearPackageFunction(player, "quest");
                  break;
               case 11: // 添加宠物
                  addPetFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 12: // 添加宠物装备
                  addPetEquipmentFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 13: // 修改金币
                  modifyMoneyFunction(player, parseInt(itemNum));
                  break;
               case 14: // 修改等级
                  modifyLevelFunction(player, parseInt(itemNum));
                  break;
               case 15: // 修改经验
                  modifyExperienceFunction(player, parseInt(itemNum));
                  break;
               case 16: // 修改血量
                  modifyHealthFunction(player, parseInt(itemNum));
                  break;
               case 17: // 修改魔法值
                  modifyMagicFunction(player, parseInt(itemNum));
                  break;
               case 18: // 无敌模式
                  toggleInvincibleFunction(player, parseInt(itemNum));
                  break;
               case 19: // 秒杀模式
                  toggleOneHitKillFunction(player, parseInt(itemNum));
                  break;
               case 20: // 解锁所有技能
                  unlockAllSkillsFunction(player);
                  break;
               case 21: // 技能冷却清零
                  clearSkillCooldownFunction(player);
                  break;
               case 22: // 修改攻击力
                  modifyAttackFunction(player, parseInt(itemNum));
                  break;
               case 23: // 修改防御力
                  modifyDefenseFunction(player, parseInt(itemNum));
                  break;
               default:
                  debugLog("功能类型 " + functionType + " 暂未实现或不支持");
                  break;
            }
            debugLog("功能执行完成");
         } catch (e:Error) {
            debugLog("功能执行失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }

         debugLog("=== 外挂功能执行结束 ===");
      }



















      // 新增：初始化游戏类引用
      private function initGameClasses():Boolean
      {
         debugLog("开始初始化游戏类引用");

         try {
            // 检查加载器状态
            if (!this.m_l || !this.m_l.contentLoaderInfo) {
               debugLog("错误：游戏加载器未准备好");
               return false;
            }

            var appDomain:Object = this.m_l.contentLoaderInfo.applicationDomain;
            if (!appDomain) {
               debugLog("错误：无法获取应用程序域");
               return false;
            }

            debugLog("应用程序域获取成功，开始加载类定义");

            // 尝试获取GamingUI类
            if (!m_gamingUI) {
               try {
                  m_gamingUI = appDomain.getDefinition("UI.GamingUI") as Class;
                  debugLog("GamingUI类加载成功");
               } catch (e:Error) {
                  debugLog("GamingUI类加载失败: " + e.message);
                  return false;
               }
            }

            // 尝试获取XMLSingle类
            if (!m_xmlSingle) {
               try {
                  m_xmlSingle = appDomain.getDefinition("UI.XMLSingle") as Class;
                  debugLog("XMLSingle类加载成功");
               } catch (e:Error) {
                  debugLog("XMLSingle类加载失败: " + e.message);
                  return false;
               }
            }

            // 尝试获取MyFunction类
            if (!m_myFunction) {
               try {
                  m_myFunction = appDomain.getDefinition("UI.MyFunction") as Class;
                  debugLog("MyFunction类加载成功");
               } catch (e:Error) {
                  debugLog("MyFunction类加载失败: " + e.message);
                  return false;
               }
            }

            debugLog("所有游戏类初始化成功");
            return true;

         } catch (e:Error) {
            debugLog("初始化游戏类失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
            return false;
         }
      }

      // 新增：获取玩家对象
      private function getPlayerObject(playerIndex:int):Object
      {
         debugLog("尝试获取玩家对象，玩家编号: " + playerIndex);

         try {
            if (!m_gamingUI) {
               debugLog("错误：GamingUI类未初始化");
               return null;
            }

            var gamingUI:Object = m_gamingUI["getInstance"]();
            if (!gamingUI) {
               debugLog("错误：无法获取GamingUI实例");
               return null;
            }

            debugLog("GamingUI实例获取成功");

            var player:Object = null;
            if (playerIndex == 1) {
               player = gamingUI.player1;
               debugLog("尝试获取player1");
            } else if (playerIndex == 2) {
               player = gamingUI.player2;
               debugLog("尝试获取player2");
            } else {
               debugLog("错误：无效的玩家编号: " + playerIndex);
               return null;
            }

            if (player) {
               debugLog("玩家对象获取成功");
               return player;
            } else {
               debugLog("错误：玩家对象为null");
               return null;
            }

         } catch (e:Error) {
            debugLog("获取玩家对象失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
         return null;
      }

      // 新增：分析玩家数据结构
      private function analyzePlayerStructure(player:Object):void
      {
         debugLog("=== 开始分析玩家数据结构 ===");

         try {
            if (!player) {
               debugLog("玩家对象为null");
               return;
            }

            debugLog("玩家对象类型: " + typeof(player));
            debugLog("玩家对象类名: " + Object(player).constructor);

            // 分析playerVO
            if (player.playerVO) {
               debugLog("playerVO存在");
               debugLog("playerVO类型: " + typeof(player.playerVO));

               // 检查常用属性
               var commonProps:Array = ["money", "level", "experienceVolume", "bloodPercent", "magicPercent"];
               for each (var prop:String in commonProps) {
                  try {
                     var value:* = player.playerVO[prop];
                     debugLog("playerVO." + prop + " = " + value + " (类型: " + typeof(value) + ")");
                  } catch (e:Error) {
                     debugLog("无法访问playerVO." + prop + ": " + e.message);
                  }
               }

               // 检查_antiwear属性
               if (player.playerVO._antiwear) {
                  debugLog("playerVO._antiwear存在");
                  var antiwearProps:Array = ["baseBloodVolume", "baseMaxMagic", "baseAttack", "baseDefence"];
                  for each (var aProp:String in antiwearProps) {
                     try {
                        var aValue:* = player.playerVO._antiwear[aProp];
                        debugLog("playerVO._antiwear." + aProp + " = " + aValue);
                     } catch (e:Error) {
                        debugLog("无法访问playerVO._antiwear." + aProp + ": " + e.message);
                     }
                  }
               } else {
                  debugLog("playerVO._antiwear不存在");
               }

               // 检查背包
               if (player.playerVO.packageEquipmentVOs) {
                  debugLog("背包存在，长度: " + player.playerVO.packageEquipmentVOs.length);
               } else {
                  debugLog("背包不存在");
               }

            } else {
               debugLog("playerVO不存在");
            }

         } catch (e:Error) {
            debugLog("分析玩家数据结构失败: " + e.message);
         }

         debugLog("=== 玩家数据结构分析完成 ===");
      }

      // 新增：创建装备
      private function createEquipment(equipmentId:int):Object
      {
         debugLog("尝试创建装备，ID: " + equipmentId);

         try {
            if (!m_xmlSingle) {
               debugLog("错误：XMLSingle类未初始化");
               return null;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle) {
               debugLog("错误：无法获取XMLSingle实例");
               return null;
            }

            debugLog("XMLSingle实例获取成功");

            // 检查equipmentXML是否存在
            if (!xmlSingle.equipmentXML) {
               debugLog("错误：equipmentXML不存在");
               return null;
            }

            debugLog("equipmentXML存在，开始创建装备");

            // 使用静态方法调用，而不是实例方法
            var equipment:Object = m_xmlSingle["getEquipmentVOByID"](equipmentId, xmlSingle.equipmentXML);
            if (equipment) {
               debugLog("装备创建成功，类型: " + typeof(equipment));
               debugLog("装备名称: " + equipment.name);
               debugLog("装备等级: " + equipment.level);
               return equipment;
            } else {
               debugLog("装备创建失败，返回null");
               return null;
            }

         } catch (e:Error) {
            debugLog("创建装备失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());

            // 尝试备用方法
            debugLog("尝试备用创建方法");
            try {
               var xmlSingle2:Object = m_xmlSingle["getInstance"]();
               var equipment2:Object = m_xmlSingle["getEquipment"](equipmentId, xmlSingle2.equipmentXML);
               if (equipment2) {
                  debugLog("备用方法创建装备成功");
                  return equipment2;
               }
            } catch (e2:Error) {
               debugLog("备用方法也失败: " + e2.message);
            }
         }
         return null;
      }

      // 新增：添加装备到背包
      private function addEquipmentToPackage(player:Object, equipment:Object):Boolean
      {
         debugLog("尝试添加装备到背包");

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return false;
            }

            if (!equipment) {
               debugLog("错误：装备对象无效");
               return false;
            }

            // 检查背包是否存在
            if (!player.playerVO.packageEquipmentVOs) {
               debugLog("错误：背包不存在");
               return false;
            }

            debugLog("背包存在，当前长度: " + player.playerVO.packageEquipmentVOs.length);
            debugLog("装备信息 - ID: " + equipment.id + ", 名称: " + equipment.name + ", 类型: " + equipment.equipmentType);

            // 尝试使用MyFunction添加装备
            if (m_myFunction) {
               try {
                  var myFunction:Object = m_myFunction["getInstance"]();
                  if (myFunction && myFunction["putInEquipmentVOToEquipmentVOVector"]) {
                     // 使用正确的参数：背包数组, 装备对象, 模式(0=正常), 保留位置(0=无)
                     var result:Boolean = myFunction["putInEquipmentVOToEquipmentVOVector"](
                        player.playerVO.packageEquipmentVOs,
                        equipment,
                        0,  // 模式参数
                        0   // 保留位置参数
                     );
                     debugLog("通过MyFunction添加装备结果: " + result);
                     if (result) {
                        return true;
                     }
                  } else {
                     debugLog("MyFunction方法不存在");
                  }
               } catch (e1:Error) {
                  debugLog("通过MyFunction添加装备失败: " + e1.message);
                  debugLog("MyFunction错误堆栈: " + e1.getStackTrace());
               }
            } else {
               debugLog("MyFunction类未初始化");
            }

            // 备用方案：直接添加到背包
            debugLog("尝试直接添加到背包");
            try {
               // 查找空位
               for (var i:int = 0; i < player.playerVO.packageEquipmentVOs.length; i++) {
                  if (player.playerVO.packageEquipmentVOs[i] == null) {
                     player.playerVO.packageEquipmentVOs[i] = equipment;
                     debugLog("直接添加装备到位置: " + i);
                     return true;
                  }
               }
               debugLog("背包已满，无法添加装备");
               return false;
            } catch (e2:Error) {
               debugLog("直接添加装备失败: " + e2.message);
               return false;
            }

         } catch (e:Error) {
            debugLog("添加装备到背包失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
         return false;
      }

      // 新增：检查装备ID是否有效
      private function isValidEquipmentId(equipmentId:int):Boolean
      {
         try {
            if (!m_xmlSingle) {
               return false;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle || !xmlSingle.equipmentXML) {
               return false;
            }

            // 检查装备ID是否存在于XML中
            var equipmentXML:XML = xmlSingle.equipmentXML;
            var equipmentNode:XMLList = equipmentXML.item.(@id == equipmentId);

            if (equipmentNode.length() > 0) {
               debugLog("装备ID " + equipmentId + " 有效，名称: " + equipmentNode[0].@name);
               return true;
            } else {
               debugLog("装备ID " + equipmentId + " 无效，在XML中未找到");
               return false;
            }

         } catch (e:Error) {
            debugLog("检查装备ID失败: " + e.message);
            return false;
         }
      }

      // 功能实现：添加装备
      private function addEquipmentFunction(player:Object, equipmentId:int, num:int):void
      {
         debugLog("开始添加装备功能，ID: " + equipmentId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效
            if (!isValidEquipmentId(equipmentId)) {
               debugLog("错误：装备ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个装备");

               var equipment:Object = createEquipment(equipmentId);
               if (equipment) {
                  if (addEquipmentToPackage(player, equipment)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个装备添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个装备添加失败");
                     break; // 如果背包满了，停止添加
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个装备创建失败");
                  break; // 如果创建失败，停止添加
               }
            }

            debugLog("装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加装备功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加宝石
      private function addGemFunction(player:Object, gemId:int, num:int):void
      {
         debugLog("开始添加宝石功能，ID: " + gemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效（宝石也是装备的一种）
            if (!isValidEquipmentId(gemId)) {
               debugLog("错误：宝石ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个宝石");

               var gem:Object = createEquipment(gemId);
               if (gem) {
                  if (addEquipmentToPackage(player, gem)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个宝石添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个宝石添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个宝石创建失败");
                  break;
               }
            }

            debugLog("宝石添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加宝石功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加消耗品
      private function addConsumableFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加消耗品功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效（消耗品也是装备的一种）
            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：消耗品ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个消耗品");

               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个消耗品添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个消耗品添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个消耗品创建失败");
                  break;
               }
            }

            debugLog("消耗品添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加消耗品功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加其他道具
      private function addOtherItemFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加其他道具功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：道具ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("其他道具添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加其他道具功能失败: " + e.message);
         }
      }

      // 功能实现：添加任务道具
      private function addQuestItemFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加任务道具功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：任务道具ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("任务道具添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加任务道具功能失败: " + e.message);
         }
      }

      // 功能实现：添加宠物
      private function addPetFunction(player:Object, petId:int, level:int):void
      {
         debugLog("开始添加宠物功能，ID: " + petId + ", 等级: " + level);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(petId)) {
               debugLog("错误：宠物ID无效，请检查good.xml文件");
               return;
            }

            var pet:Object = createEquipment(petId);
            if (pet) {
               if (addEquipmentToPackage(player, pet)) {
                  debugLog("宠物添加成功");
                  refreshUI();
               } else {
                  debugLog("宠物添加失败");
               }
            } else {
               debugLog("宠物创建失败");
            }

         } catch (e:Error) {
            debugLog("添加宠物功能失败: " + e.message);
         }
      }

      // 功能实现：添加宠物装备
      private function addPetEquipmentFunction(player:Object, equipId:int, num:int):void
      {
         debugLog("开始添加宠物装备功能，ID: " + equipId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(equipId)) {
               debugLog("错误：宠物装备ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var equipment:Object = createEquipment(equipId);
               if (equipment) {
                  if (addEquipmentToPackage(player, equipment)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("宠物装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加宠物装备功能失败: " + e.message);
         }
      }

      // 功能实现：清理背包
      private function clearPackageFunction(player:Object, type:String):void
      {
         debugLog("开始清理背包功能，类型: " + type);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var packageArray:Object = player.playerVO.packageEquipmentVOs;
            if (!packageArray) {
               debugLog("错误：背包不存在");
               return;
            }

            var clearCount:int = 0;
            var totalCount:int = packageArray.length;

            debugLog("背包总容量: " + totalCount);

            // 清理所有物品
            for (var i:int = 0; i < totalCount; i++) {
               if (packageArray[i] != null) {
                  packageArray[i] = null;
                  clearCount++;
               }
            }

            debugLog("背包清理完成，清理了 " + clearCount + " 个物品");

            if (clearCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("清理背包功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：秒杀模式
      private function toggleOneHitKillFunction(player:Object, enable:int):void
      {
         debugLog("开始切换秒杀模式，状态: " + (enable == 1 ? "开启" : "关闭"));

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            if (enable == 1) {
               // 开启秒杀模式 - 设置超高攻击力
               if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseAttack")) {
                  player.playerVO._antiwear.baseAttack = 999999999;
                  debugLog("通过_antiwear设置超高攻击力");
               } else {
                  // 尝试其他属性
                  var attackProps:Array = ["attack", "offensiveValue", "damage"];
                  for each (var prop:String in attackProps) {
                     try {
                        if (player.playerVO.hasOwnProperty(prop)) {
                           player.playerVO[prop] = 999999999;
                           debugLog("通过playerVO." + prop + "设置超高攻击力");
                           break;
                        }
                     } catch (e:Error) {
                        debugLog("设置" + prop + "失败: " + e.message);
                     }
                  }
               }
            } else {
               // 关闭秒杀模式 - 恢复正常攻击力
               if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseAttack")) {
                  player.playerVO._antiwear.baseAttack = 100; // 恢复默认值
                  debugLog("恢复正常攻击力");
               }
            }

            refreshUI();
            debugLog("秒杀模式切换完成: " + (enable == 1 ? "开启" : "关闭"));

         } catch (e:Error) {
            debugLog("切换秒杀模式失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：解锁所有技能
      private function unlockAllSkillsFunction(player:Object):void
      {
         debugLog("开始解锁所有技能功能");

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            // 尝试解锁玩家技能
            var skillUnlocked:Boolean = false;

            if (player.playerVO.playerActiveSkillVOs) {
               debugLog("找到玩家主动技能，开始解锁");
               for (var i:int = 0; i < player.playerVO.playerActiveSkillVOs.length; i++) {
                  var skill:Object = player.playerVO.playerActiveSkillVOs[i];
                  if (skill && skill._antiwear) {
                     var originalLevel:int = skill._antiwear.level;
                     skill._antiwear.level = skill._antiwear.maxLevel || 10;
                     debugLog("技能 " + i + " 等级从 " + originalLevel + " 提升到 " + skill._antiwear.level);
                     skillUnlocked = true;
                  }
               }
            }

            if (skillUnlocked) {
               refreshUI();
               debugLog("技能解锁完成");
            } else {
               debugLog("未找到可解锁的技能");
            }

         } catch (e:Error) {
            debugLog("解锁所有技能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：清除技能冷却
      private function clearSkillCooldownFunction(player:Object):void
      {
         debugLog("开始清除技能冷却功能");

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            // 这个功能需要更深入的游戏机制了解
            debugLog("技能冷却清除功能需要进一步实现");

         } catch (e:Error) {
            debugLog("清除技能冷却失败: " + e.message);
         }
      }

      // 功能实现：清理背包
      private function clearPackageFunction(player:Object, type:String):void
      {
         try {
            var packageArray:Object;
            switch (type) {
               case "equipment":
                  packageArray = player.playerVO.packageEquipmentVOs;
                  break;
               default:
                  packageArray = player.playerVO.packageEquipmentVOs;
                  break;
            }

            if (packageArray) {
               for (var i:int = 0; i < packageArray.length; i++) {
                  packageArray[i] = null;
               }
               refreshUI();
               trace("成功清理" + type + "背包");
            }
         } catch (e:Error) {
            trace("清理背包失败: " + e.message);
         }
      }

      // 功能实现：修改金币
      private function modifyMoneyFunction(player:Object, amount:int):void
      {
         debugLog("开始修改金币，目标值: " + amount);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }



            // 记录原始值
            var originalMoney:* = player.playerVO.money;
            debugLog("原始金币值: " + originalMoney + " (类型: " + typeof(originalMoney) + ")");

            // 修改金币
            var success:Boolean = false;

            // 方法1：直接修改
            try {
               player.playerVO.money = amount;
               debugLog("直接赋值成功");
               success = true;
            } catch (e1:Error) {
               debugLog("直接赋值失败: " + e1.message);
            }

            // 方法2：通过_antiwear修改
            if (!success && player.playerVO._antiwear) {
               try {
                  player.playerVO._antiwear.money = amount;
                  debugLog("通过_antiwear修改成功");
                  success = true;
               } catch (e2:Error) {
                  debugLog("通过_antiwear修改失败: " + e2.message);
               }
            }

            // 方法3：绕过加密修改
            if (!success) {
               try {
                  // 直接修改加密前的数据
                  if (player.playerVO._antiwear && player.playerVO._antiwear.data) {
                     player.playerVO._antiwear.data.money = amount;
                     debugLog("通过加密数据修改成功");
                     success = true;
                  }
               } catch (e3:Error) {
                  debugLog("通过加密数据修改失败: " + e3.message);
               }
            }

            // 验证修改结果
            var newMoney:* = player.playerVO.money;
            debugLog("修改后金币值: " + newMoney);

            if (success && newMoney == amount) {
               debugLog("金币修改成功！");
               refreshUI();
            } else {
               debugLog("金币修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改金币失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }



      // 功能实现：修改等级
      private function modifyLevelFunction(player:Object, level:int):void
      {
         debugLog("开始修改等级，目标值: " + level);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var originalLevel:* = player.playerVO.level;
            debugLog("原始等级: " + originalLevel);

            // 尝试修改等级
            player.playerVO.level = level;

            var newLevel:* = player.playerVO.level;
            debugLog("修改后等级: " + newLevel);

            if (newLevel == level) {
               debugLog("等级修改成功！");
               refreshUI();
            } else {
               debugLog("等级修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改等级失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改经验
      private function modifyExperienceFunction(player:Object, exp:int):void
      {
         debugLog("开始修改经验，目标值: " + exp);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var originalExp:* = player.playerVO.experienceVolume;
            debugLog("原始经验: " + originalExp);

            player.playerVO.experienceVolume = exp;

            var newExp:* = player.playerVO.experienceVolume;
            debugLog("修改后经验: " + newExp);

            if (newExp == exp) {
               debugLog("经验修改成功！");
               refreshUI();
            } else {
               debugLog("经验修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改经验失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改血量
      private function modifyHealthFunction(player:Object, health:int):void
      {
         debugLog("开始修改血量，目标值: " + health);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            // 尝试多种方式修改血量
            var success:Boolean = false;

            // 方式1：直接修改baseBloodVolume
            if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseBloodVolume")) {
               try {
                  var originalHealth:* = player.playerVO._antiwear.baseBloodVolume;
                  debugLog("原始血量: " + originalHealth);

                  player.playerVO._antiwear.baseBloodVolume = health;
                  player.playerVO.bloodPercent = 1.0; // 满血状态

                  debugLog("通过_antiwear.baseBloodVolume修改成功");
                  success = true;
               } catch (e1:Error) {
                  debugLog("通过_antiwear.baseBloodVolume修改失败: " + e1.message);
               }
            }

            // 方式2：尝试其他可能的属性
            if (!success) {
               var healthProps:Array = ["bloodVolume", "maxBloodVolume", "hp", "maxHp"];
               for each (var prop:String in healthProps) {
                  try {
                     if (player.playerVO.hasOwnProperty(prop)) {
                        player.playerVO[prop] = health;
                        debugLog("通过playerVO." + prop + "修改成功");
                        success = true;
                        break;
                     }
                  } catch (e2:Error) {
                     debugLog("通过playerVO." + prop + "修改失败: " + e2.message);
                  }
               }
            }

            if (success) {
               debugLog("血量修改成功！");
               refreshUI();
            } else {
               debugLog("血量修改失败，所有方式都无效");
            }

         } catch (e:Error) {
            debugLog("修改血量失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改魔法值
      private function modifyMagicFunction(player:Object, magic:int):void
      {
         debugLog("开始修改魔法值，目标值: " + magic);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var success:Boolean = false;

            // 尝试通过_antiwear修改
            if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseMaxMagic")) {
               try {
                  var originalMagic:* = player.playerVO._antiwear.baseMaxMagic;
                  debugLog("原始魔法值: " + originalMagic);

                  player.playerVO._antiwear.baseMaxMagic = magic;
                  player.playerVO.magicPercent = 1.0; // 满魔状态

                  debugLog("通过_antiwear.baseMaxMagic修改成功");
                  success = true;
               } catch (e1:Error) {
                  debugLog("通过_antiwear.baseMaxMagic修改失败: " + e1.message);
               }
            }

            // 尝试其他可能的属性
            if (!success) {
               var magicProps:Array = ["magicVolume", "maxMagic", "mp", "maxMp"];
               for each (var prop:String in magicProps) {
                  try {
                     if (player.playerVO.hasOwnProperty(prop)) {
                        player.playerVO[prop] = magic;
                        debugLog("通过playerVO." + prop + "修改成功");
                        success = true;
                        break;
                     }
                  } catch (e2:Error) {
                     debugLog("通过playerVO." + prop + "修改失败: " + e2.message);
                  }
               }
            }

            if (success) {
               debugLog("魔法值修改成功！");
               refreshUI();
            } else {
               debugLog("魔法值修改失败，所有方式都无效");
            }

         } catch (e:Error) {
            debugLog("修改魔法值失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：无敌模式
      private function toggleInvincibleFunction(player:Object, enable:int):void
      {
         debugLog("开始切换无敌模式，状态: " + (enable == 1 ? "开启" : "关闭"));

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var success:Boolean = false;

            // 尝试设置无敌状态
            try {
               player.playerVO.isInvincible = (enable == 1);
               if (enable == 1) {
                  player.playerVO.endInvincibleTime = Number.MAX_VALUE;
               } else {
                  player.playerVO.endInvincibleTime = 0;
               }
               debugLog("无敌模式设置成功");
               success = true;
            } catch (e1:Error) {
               debugLog("设置无敌模式失败: " + e1.message);
            }

            // 尝试通过_antiwear设置
            if (!success && player.playerVO._antiwear) {
               try {
                  player.playerVO._antiwear.isInvincible = (enable == 1);
                  debugLog("通过_antiwear设置无敌模式成功");
                  success = true;
               } catch (e2:Error) {
                  debugLog("通过_antiwear设置无敌模式失败: " + e2.message);
               }
            }

            if (success) {
               debugLog("无敌模式切换成功！");
               refreshUI();
            } else {
               debugLog("无敌模式切换失败");
            }

         } catch (e:Error) {
            debugLog("切换无敌模式失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改攻击力
      private function modifyAttackFunction(player:Object, attack:int):void
      {
         debugLog("开始修改攻击力，目标值: " + attack);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var success:Boolean = false;

            // 尝试通过_antiwear修改
            if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseAttack")) {
               try {
                  var originalAttack:* = player.playerVO._antiwear.baseAttack;
                  debugLog("原始攻击力: " + originalAttack);

                  player.playerVO._antiwear.baseAttack = attack;
                  debugLog("通过_antiwear.baseAttack修改成功");
                  success = true;
               } catch (e1:Error) {
                  debugLog("通过_antiwear.baseAttack修改失败: " + e1.message);
               }
            }

            // 尝试其他可能的属性
            if (!success) {
               var attackProps:Array = ["attack", "offensiveValue", "damage"];
               for each (var prop:String in attackProps) {
                  try {
                     if (player.playerVO.hasOwnProperty(prop)) {
                        player.playerVO[prop] = attack;
                        debugLog("通过playerVO." + prop + "修改成功");
                        success = true;
                        break;
                     }
                  } catch (e2:Error) {
                     debugLog("通过playerVO." + prop + "修改失败: " + e2.message);
                  }
               }
            }

            if (success) {
               debugLog("攻击力修改成功！");
               refreshUI();
            } else {
               debugLog("攻击力修改失败，所有方式都无效");
            }

         } catch (e:Error) {
            debugLog("修改攻击力失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加宝石
      private function addGemFunction(player:Object, gemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var gem:Object = createEquipment(gemId);
               if (gem) {
                  addEquipmentToPackage(player, gem);
               }
            }
            refreshUI();
            trace("成功添加宝石 ID:" + gemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加宝石失败: " + e.message);
         }
      }

      // 功能实现：添加消耗品
      private function addConsumableFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加消耗品 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加消耗品失败: " + e.message);
         }
      }

      // 功能实现：添加其他道具
      private function addOtherItemFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加其他道具 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加其他道具失败: " + e.message);
         }
      }

      // 功能实现：添加任务道具
      private function addQuestItemFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加任务道具 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加任务道具失败: " + e.message);
         }
      }

      // 功能实现：添加宠物
      private function addPetFunction(player:Object, petId:int, level:int):void
      {
         try {
            // 这里需要根据游戏的宠物系统来实现
            // 暂时使用通用的装备添加方法
            var pet:Object = createEquipment(petId);
            if (pet) {
               addEquipmentToPackage(player, pet);
            }
            refreshUI();
            trace("成功添加宠物 ID:" + petId + " 等级:" + level);
         } catch (e:Error) {
            trace("添加宠物失败: " + e.message);
         }
      }

      // 功能实现：添加宠物装备
      private function addPetEquipmentFunction(player:Object, equipId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var equipment:Object = createEquipment(equipId);
               if (equipment) {
                  addEquipmentToPackage(player, equipment);
               }
            }
            refreshUI();
            trace("成功添加宠物装备 ID:" + equipId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加宠物装备失败: " + e.message);
         }
      }

      // 功能实现：秒杀模式
      private function toggleOneHitKillFunction(player:Object, enable:int):void
      {
         try {
            if (enable == 1) {
               player.playerVO.baseAttack = 999999999;
               player.playerVO.offensiveValue = 999999999;
            } else {
               // 恢复正常攻击力，这里需要保存原始值
               player.playerVO.baseAttack = 1000; // 默认值
               player.playerVO.offensiveValue = 1000;
            }
            refreshUI();
            trace("秒杀模式: " + (enable == 1 ? "开启" : "关闭"));
         } catch (e:Error) {
            trace("切换秒杀模式失败: " + e.message);
         }
      }

      // 功能实现：解锁所有技能
      private function unlockAllSkillsFunction(player:Object):void
      {
         try {
            // 解锁玩家技能
            if (player.playerVO && player.playerVO.playerActiveSkillVOs) {
               for (var i:int = 0; i < player.playerVO.playerActiveSkillVOs.length; i++) {
                  var skill:Object = player.playerVO.playerActiveSkillVOs[i];
                  if (skill && skill._antiwear) {
                     skill._antiwear.level = skill._antiwear.maxLevel || 10;
                  }
               }
            }
            refreshUI();
            trace("成功解锁所有技能");
         } catch (e:Error) {
            trace("解锁所有技能失败: " + e.message);
         }
      }

      // 功能实现：清除技能冷却
      private function clearSkillCooldownFunction(player:Object):void
      {
         try {
            // 这里需要根据游戏的技能系统来实现
            trace("清除技能冷却功能需要进一步实现");
         } catch (e:Error) {
            trace("清除技能冷却失败: " + e.message);
         }
      }

      // 功能实现：修改防御力
      private function modifyDefenseFunction(player:Object, defense:int):void
      {
         debugLog("开始修改防御力，目标值: " + defense);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var success:Boolean = false;

            // 尝试通过_antiwear修改
            if (player.playerVO._antiwear && player.playerVO._antiwear.hasOwnProperty("baseDefence")) {
               try {
                  var originalDefense:* = player.playerVO._antiwear.baseDefence;
                  debugLog("原始防御力: " + originalDefense);

                  player.playerVO._antiwear.baseDefence = defense;
                  debugLog("通过_antiwear.baseDefence修改成功");
                  success = true;
               } catch (e1:Error) {
                  debugLog("通过_antiwear.baseDefence修改失败: " + e1.message);
               }
            }

            // 尝试其他可能的属性
            if (!success) {
               var defenseProps:Array = ["defense", "defence", "armor"];
               for each (var prop:String in defenseProps) {
                  try {
                     if (player.playerVO.hasOwnProperty(prop)) {
                        player.playerVO[prop] = defense;
                        debugLog("通过playerVO." + prop + "修改成功");
                        success = true;
                        break;
                     }
                  } catch (e2:Error) {
                     debugLog("通过playerVO." + prop + "修改失败: " + e2.message);
                  }
               }
            }

            if (success) {
               debugLog("防御力修改成功！");
               refreshUI();
            } else {
               debugLog("防御力修改失败，所有方式都无效");
            }

         } catch (e:Error) {
            debugLog("修改防御力失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }



      // 辅助方法：刷新UI
      private function refreshUI():void
      {
         debugLog("开始刷新UI");

         try {
            if (!m_gamingUI) {
               debugLog("错误：GamingUI类未初始化");
               return;
            }

            var gamingUI:Object = m_gamingUI["getInstance"]();
            if (!gamingUI) {
               debugLog("错误：无法获取GamingUI实例");
               return;
            }

            // 尝试多种刷新方法
            var refreshMethods:Array = ["refresh", "refreshUI", "updateUI", "refreshAll"];
            var success:Boolean = false;

            for each (var method:String in refreshMethods) {
               try {
                  if (gamingUI[method] is Function) {
                     gamingUI[method](2); // 传入参数2表示刷新背包等UI
                     debugLog("通过" + method + "方法刷新UI成功");
                     success = true;
                     break;
                  }
               } catch (e1:Error) {
                  debugLog("通过" + method + "方法刷新UI失败: " + e1.message);
               }
            }

            if (!success) {
               debugLog("所有刷新方法都失败，尝试触发事件");
               try {
                  // 尝试触发刷新事件
                  gamingUI.dispatchEvent(new Event("refreshAtt"));
                  debugLog("触发refreshAtt事件成功");
               } catch (e2:Error) {
                  debugLog("触发refreshAtt事件失败: " + e2.message);
               }
            }

         } catch (e:Error) {
            debugLog("刷新UI失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      internal function frame1() : *
      {
         this.conn = new LocalConnection();
         this.conn.client = this;
         this.m_l = new Loader();
         this.m_l.load(new URLRequest("game.swf"));
         this.m_l.contentLoaderInfo.addEventListener(Event.COMPLETE,this.__);
         Security.allowDomain("*");
         Cc.startOnStage(this,"");
         Cc.visible = true;
         Cc.commandLine = true;
         Cc.config.commandLineAllowed = true;
      }
   }
}

